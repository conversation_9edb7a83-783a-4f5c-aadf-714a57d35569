package br.com.alice.api.event.controllers

import br.com.alice.api.event.ControllerTestHelper
import br.com.alice.api.event.events.netlex.NetLexCreateHealthProfessionalEvent
import br.com.alice.api.event.model.Address
import br.com.alice.api.event.model.Contact
import br.com.alice.api.event.model.HealthProfessional
import br.com.alice.api.event.model.NetLexHealthProfessionalRequest
import br.com.alice.api.event.model.Phone
import br.com.alice.api.event.model.Provider
import br.com.alice.api.event.services.NetLexAuthService
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import com.google.gson.Gson
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.mockk.called
import io.mockk.coVerify
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class NetLexControllerTest : ControllerTestHelper() {
    private val authService: NetLexAuthService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val authController = NetLexController(authService, kafkaProducerService)

    private val clientId = "ba74859d-964b-4acb-9a62-ec5c83c48555"
    private val clientSecret = "d2c7db28-d57c-4060-b055-2e4dc52fee13"

    private val request = AuthRequest(
        clientId = clientId,
        clientSecret = clientSecret
    )
    private val bodyString = Gson().toJson(request)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { authController }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(authService)

    @Test
    fun `#authorize should return 200 with token if valid credentials`() {
        val expected = AuthResponse(token = token)

        coEvery { authService.authorize(clientId, clientSecret) } returns token

        post("/auth/netlex", body = bodyString) { response ->
            assertEquals(HttpStatusCode.OK, response.status)
            val json = response.bodyAsText()
            val parsed = Gson().fromJson(json, AuthResponse::class.java)
            assertEquals(expected, parsed)
        }

        coVerify(exactly = 1) { authService.authorize(any(), any()) }
    }

    @Test
    fun `#authorize should return 401 if not authorized`() {
        coEvery { authService.authorize(clientId, clientSecret) } returns null

        post("/auth/netlex", body = bodyString) { response ->
            assertEquals(HttpStatusCode.Unauthorized, response.status)
        }

        coVerify(exactly = 1) { authService.authorize(any(), any()) }
    }

    @Test
    fun `#createHealthProfessional should return 200 when event is produced successfully`() {
        val request = request()

        val body = Gson().toJson(request)

        coEvery {
            kafkaProducerService.produce(match {
                it is NetLexCreateHealthProfessionalEvent && it.payload == request
            })
        } returns mockk()

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body) { response ->
                assertEquals(HttpStatusCode.OK, response.status)

                coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when kafka producer fails`() {
        val request = request()
        val body = Gson().toJson(request)

        coEvery { kafkaProducerService.produce(any()) } throws Exception("Kafka error")

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.InternalServerError, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("Failed to create Health Professional"))

                coVerify(exactly = 1) { kafkaProducerService.produce(any()) }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 401 when not authenticated`() {
        val request = request()
        val body = Gson().toJson(request)

        post("/netlex/health_professionals", body = body) { response ->
            assertEquals(HttpStatusCode.Unauthorized, response.status)

            coVerify { kafkaProducerService wasNot called }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when required fields are missing`() {
        val request = request().copy(
            healthProfessional = request().healthProfessional.copy(firstName = "")
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("firstName é obrigatório"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when email is invalid`() {
        val request = request().copy(
            healthProfessional = request().healthProfessional.copy(email = "invalid-email")
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("email inválido"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when CPF is invalid`() {
        val request = request().copy(
            healthProfessional = request().healthProfessional.copy(nationalId = "12345678900")
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("CPF inválido"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when gender is invalid`() {
        val request = request().copy(
            healthProfessional = request().healthProfessional.copy(gender = "INVALID_GENDER")
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("gender inválido"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when councilType is invalid`() {
        val request = request().copy(
            healthProfessional = request().healthProfessional.copy(councilType = "INVALID_COUNCIL")
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("councilType inválido"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when state is invalid`() {
        val request = request().copy(
            healthProfessional = request().healthProfessional.copy(councilState = "XX")
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("councilState inválido"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when phone type is invalid`() {
        val invalidContact = request().healthProfessional.contacts.first().copy(
            phones = listOf(Phone(type = "INVALID_TYPE", number = "**********"))
        )
        val request = request().copy(
            healthProfessional = request().healthProfessional.copy(
                contacts = listOf(invalidContact)
            )
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("phone type inválido"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when modality is invalid`() {
        val invalidContact = request().healthProfessional.contacts.first().copy(
            modality = "INVALID_MODALITY"
        )
        val request = request().copy(
            healthProfessional = request().healthProfessional.copy(
                contacts = listOf(invalidContact)
            )
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("modality inválido"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    @Test
    fun `#createHealthProfessional should return 400 when provider CNPJ is invalid`() {
        val request = request().copy(
            provider = request().provider.copy(cnpj = "12345678000100")
        )
        val body = Gson().toJson(request)

        netLexAuthenticated(token) {
            post("/netlex/health_professionals", body = body) { response ->
                assertEquals(HttpStatusCode.BadRequest, response.status)

                val json = response.bodyAsText()
                assertTrue(json.contains("provider cnpj inválido"))

                coVerify { kafkaProducerService wasNot called }
            }
        }
    }

    private fun request() = NetLexHealthProfessionalRequest(
        healthProfessional = HealthProfessional(
            firstName = "João",
            lastName = "Silva",
            email = "<EMAIL>",
            nationalId = "07331706098", // Valid CPF
            birthdate = "1980-01-01",
            gender = "MALE",
            profileImageUrl = "",
            profileBio = "",
            education = "Formado em Medicina pela USP",
            curiosity = "Gosto de trilhas",
            councilType = "CRM",
            councilNumber = "123456",
            councilState = "SP",
            specialty = "Clínico Geral",
            subSpecialties = emptyList(),
            contacts = listOf(
                Contact(
                    address = Address(
                        street = "Rua das Flores",
                        number = "123",
                        complement = "Apto 45",
                        neighborhood = "Jardim das Rosas",
                        city = "São Paulo",
                        state = "SP",
                        zipcode = "01234567",
                        country = "BR"
                    ),
                    phones = listOf(
                        Phone(type = "WHATSAPP", number = "**********"),
                        Phone(type = "MOBILE", number = "***********")
                    ),
                    modality = "PRESENTIAL"
                )
            )
        ),
        provider = Provider(
            name = "Saúde - Unidade Centro",
            cnpj = "11.222.333/0001-81", // Valid CNPJ
            cnes = "1234567",
            bankCode = "001",
            agencyNumber = "1234-x",
            accountNumber = "56789-0",
            phones = listOf(
                Phone(type = "MOBILE", number = "***********"),
                Phone(type = "WHATSAPP", number = "***********")
            ),
            address = Address(
                street = "Av. Paulista",
                number = "1000",
                complement = "10º andar",
                neighborhood = "Bela Vista",
                city = "São Paulo",
                state = "SP",
                zipcode = "01310-100",
                country = "BR"
            )
        )
    )
}
